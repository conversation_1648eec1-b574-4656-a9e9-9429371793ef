import { ControlType, getPropertyControlsNew } from "@repo/shared/components/editor/property-controls-new";
import { addPropertyControlHandler } from "../../property-controls/propertyControlHandler";
import { Widget } from "@repo/shared/lib/types/editor";
import { useWidgetSettings } from "@/lib/hooks/useWidgetSettings";
import { Label } from "@repo/shared/components/ui/label";

import { Textarea } from "@repo/shared/components/ui/textarea";
import { ValueSourcePicker } from "@repo/shared/lib/dynamic-values/components/ValueSourcePicker";
import { DynamicText } from "@repo/shared/lib/types/widgetSettings";
import { useState, useRef } from "react";
import { Type, Sparkles } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@repo/shared/components/ui/popover";
import { Badge } from "@repo/shared/components/ui/badge";

function DynamicTextPropertyControl(props: {widget: Widget, property: string}) {
    const { settings, updateSettings } = useWidgetSettings(props.widget)
    const propertyControls = getPropertyControlsNew(props.widget.componentName)
    const propertyControl = propertyControls?.[props.property]
    
    // Get current value or default
    const currentValue: DynamicText = settings[props.property] ?? propertyControl?.defaultValue ?? {
        expression: ""
    }
    
    const [isPopoverOpen, setIsPopoverOpen] = useState(false)
    const textareaRef = useRef<HTMLTextAreaElement>(null)

    const handleExpressionChange = (expression: string) => {
        const newValue: DynamicText = {
            ...currentValue,
            expression
        }
        updateSettings({[props.property]: newValue})
    }

    const handleVariableSelect = (path: string) => {
        const variable = `{${path}}`
        const textarea = textareaRef.current

        if (textarea) {
            const start = textarea.selectionStart
            const end = textarea.selectionEnd
            const currentText = currentValue.expression
            const newText = currentText.slice(0, start) + variable + currentText.slice(end)

            handleExpressionChange(newText)

            // Set cursor position after the inserted variable
            setTimeout(() => {
                textarea.focus()
                textarea.setSelectionRange(start + variable.length, start + variable.length)
            }, 0)
        } else {
            // Fallback: append to end
            handleExpressionChange(currentValue.expression + variable)
        }

        setIsPopoverOpen(false)
    }

    return (
        <div className="space-y-3">
            <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">{propertyControl?.title ?? props.property}</Label>
            </div>

            {/* Expression Input */}
            <div className="space-y-2">
                <Textarea
                    ref={textareaRef}
                    value={currentValue.expression}
                    onChange={(e) => handleExpressionChange(e.target.value)}
                    placeholder="Enter text..."
                    className="min-h-[80px] resize-none focus:border-primary transition-colors"
                />

                {/* Add dynamic value text link */}
                <div className="flex ">
                    <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                        <PopoverTrigger asChild>
                            <button
                                type="button"
                                className="text-xs text-muted-foreground hover:text-primary transition-colors cursor-pointer underline-offset-4 hover:underline"
                            >
                                + Dynamic data
                            </button>
                        </PopoverTrigger>
                        <PopoverContent className="w-96 p-0" align="start">
                            <div className="p-3 border-b bg-muted/50">
                                <div className="flex items-center gap-2">
                                    <Type className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Insert Dynamic Value</span>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    Select a value to insert at cursor position
                                </p>
                            </div>
                            <div className="p-3">
                                <ValueSourcePicker
                                    onSelect={handleVariableSelect}
                                    placeholder="Search for dynamic values..."
                                    className="border-0"
                                />
                            </div>
                        </PopoverContent>
                    </Popover>
                </div>
            </div>

         
        </div>
    )
}

addPropertyControlHandler(ControlType.DynamicText, (props: {widget: Widget, property: string}) => <DynamicTextPropertyControl {...props}/>)
